#!/usr/bin/env python3
"""
简单播放器测试
"""

import subprocess
import sys
from pathlib import Path

def test_simple_mpv():
    """测试最简单的MPV命令"""
    print("🎬 测试简单MPV命令...")
    
    test_video = Path("resources/test.mp4")
    test_subtitle = Path("output/test_generated.srt")
    
    if not test_video.exists():
        print(f"❌ 测试视频不存在: {test_video}")
        return False
    
    # 最简单的命令
    command = ["mpv", str(test_video), "--force-window=yes"]
    
    if test_subtitle.exists():
        command.append(f"--sub-file={test_subtitle}")
        print(f"📝 将加载字幕: {test_subtitle}")
    
    print(f"🚀 执行命令: {' '.join(command)}")
    
    try:
        # 启动MPV（不等待完成）
        process = subprocess.Popen(command)
        print(f"✅ MPV进程已启动，PID: {process.pid}")
        print("💡 应该会看到MPV窗口出现")
        return True
        
    except Exception as e:
        print(f"❌ 启动MPV失败: {e}")
        return False

def test_player_service():
    """测试播放器服务"""
    print("\n🎮 测试播放器服务...")
    
    # 添加src目录到Python路径
    sys.path.insert(0, str(Path(__file__).parent / "src"))
    
    try:
        from src.services.player_service import PlayerService
        
        test_video = Path("resources/test.mp4")
        test_subtitle = Path("output/test_generated.srt")
        
        if not test_video.exists():
            print(f"❌ 测试视频不存在: {test_video}")
            return False
        
        # 创建播放器服务
        player_service = PlayerService()
        print("✅ PlayerService初始化成功")
        
        # 播放视频
        subtitle_path = str(test_subtitle) if test_subtitle.exists() else None
        player_service.play_video_with_subtitles(str(test_video), subtitle_path)
        
        print("✅ 播放器服务调用成功")
        print("💡 应该会看到MPV窗口出现")
        
        return True
        
    except Exception as e:
        print(f"❌ 播放器服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎬 简单播放器测试")
    print("=" * 30)
    
    # 测试简单MPV命令
    if test_simple_mpv():
        print("✅ 简单MPV测试成功")
    else:
        print("❌ 简单MPV测试失败")
        return 1
    
    # 测试播放器服务
    if test_player_service():
        print("✅ 播放器服务测试成功")
    else:
        print("❌ 播放器服务测试失败")
        return 1
    
    print("\n🎉 测试完成！如果看到MPV窗口，说明外部播放器工作正常。")
    return 0

if __name__ == "__main__":
    sys.exit(main())
