"""
播放器服务模块
负责视频播放和字幕显示
"""

import subprocess
import shutil
import logging
from pathlib import Path
from typing import Optional

from ..core.exceptions import PlayerError
from ..config.settings import settings

logger = logging.getLogger(__name__)


class PlayerService:
    """播放器服务类（基于MPV命令行）"""
    
    def __init__(self, mpv_path: str = None):
        self.mpv_path = mpv_path or settings.MPV_PATH
        self._check_mpv_availability()
    
    def _check_mpv_availability(self):
        """检查MPV是否可用"""
        if not shutil.which(self.mpv_path):
            raise PlayerError(f"MPV not found at '{self.mpv_path}'. Please ensure MPV is installed and in PATH.")
    
    def play_video_with_subtitles(self, video_path: str, subtitle_path: Optional[str] = None):
        """
        使用MPV播放视频并加载字幕
        首先尝试优化配置，如果失败则使用简单配置
        """
        # 先尝试简单配置
        if self._try_simple_playback(video_path, subtitle_path):
            return

        # 如果简单配置失败，使用完整配置
        self._play_with_full_config(video_path, subtitle_path)

    def _try_simple_playback(self, video_path: str, subtitle_path: Optional[str] = None) -> bool:
        """尝试使用简单配置播放"""
        try:
            video_path = Path(video_path)
            if not video_path.exists():
                return False

            # macOS优化的MPV命令
            command = [
                "mpv",
                str(video_path),
                "--force-window=yes",  # 强制显示窗口
                "--geometry=50%:50%",  # 设置窗口位置
                "--autofit=80%",  # 自动调整窗口大小
                "--ontop",  # 窗口置顶
                "--focus-on-open",  # 打开时获得焦点
            ]

            # 添加macOS特定的选项
            import platform
            if platform.system() == "Darwin":
                command.extend([
                    "--ao=coreaudio",  # 使用CoreAudio
                    "--cocoa-force-dedicated-gpu=yes",  # 强制使用独立GPU
                ])

            if subtitle_path:
                subtitle_path = Path(subtitle_path)
                if subtitle_path.exists():
                    command.extend([
                        f"--sub-file={subtitle_path}",
                        "--sub-auto=fuzzy",  # 自动加载字幕
                        "--sub-font-size=36",  # 字幕大小
                    ])

            logger.info(f"Trying simple MPV command: {' '.join(command)}")

            # 设置环境变量确保窗口显示
            env = os.environ.copy()
            if platform.system() == "Darwin":
                # 确保MPV可以创建窗口
                env['DISPLAY'] = ':0'

            # 启动播放器
            process = subprocess.Popen(
                command,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )

            # 检查是否成功启动
            import time
            time.sleep(1)
            if process.poll() is None:
                logger.info(f"Simple MPV playback started successfully (PID: {process.pid})")
                return True
            else:
                _, stderr = process.communicate()
                if stderr:
                    logger.warning(f"Simple MPV stderr: {stderr}")
                logger.warning("Simple MPV playback failed, trying full config")
                return False

        except Exception as e:
            logger.warning(f"Simple playback failed: {e}")
            return False

    def _play_with_full_config(self, video_path: str, subtitle_path: Optional[str] = None):
        """使用完整配置播放视频"""
        video_path = Path(video_path)
        if not video_path.exists():
            raise PlayerError(f"Video file not found: {video_path}")

        # 构建MPV命令，添加适当的参数以确保兼容性
        command = [
            self.mpv_path,
            str(video_path),
            "--force-window=yes",  # 强制显示窗口
            "--keep-open=yes",  # 播放结束后保持窗口打开
        ]

        # 添加平台特定的配置
        import platform
        if platform.system() == "Darwin":  # macOS
            command.extend([
                "--ao=coreaudio",  # 使用CoreAudio音频输出
                "--hwdec=auto",  # 自动硬件解码
                "--geometry=50%:50%",  # 设置窗口位置
                "--autofit=80%",  # 自动调整窗口大小为屏幕的80%
                "--ontop",  # 窗口置顶，确保可见
                "--focus-on-open",  # 打开时获得焦点
            ])
        else:
            command.extend([
                "--vo=gpu",  # 其他系统使用GPU
                "--hwdec=auto",  # 自动硬件解码
            ])

        if subtitle_path:
            subtitle_path = Path(subtitle_path)
            if subtitle_path.exists():
                command.append(f"--sub-file={subtitle_path}")
                command.append("--sub-auto=fuzzy")  # 自动加载字幕
                logger.info(f"Loading subtitles: {subtitle_path}")
            else:
                logger.warning(f"Subtitle file not found: {subtitle_path}")

        logger.info(f"Playing video with full config: {' '.join(command)}")

        try:
            # 在后台启动MPV，不等待完成
            process = subprocess.Popen(
                command,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.PIPE,
                text=True
            )

            logger.info(f"Full config MPV started with PID: {process.pid}")

            # 检查进程是否立即失败
            import time
            time.sleep(0.5)
            if process.poll() is not None:
                _, stderr = process.communicate()
                if stderr:
                    logger.warning(f"MPV stderr: {stderr}")
                if process.returncode != 0:
                    raise PlayerError(f"MPV failed to start (exit code: {process.returncode})")

        except FileNotFoundError:
            raise PlayerError(f"MPV executable not found: {self.mpv_path}")
        except Exception as e:
            raise PlayerError(f"Error during video playback: {e}")


# 为将来的GUI集成预留的MPV集成类
class MPVWidget:
    """MPV播放器组件（为GUI集成预留）"""
    
    def __init__(self):
        self.mpv_process = None
        self.is_playing = False
        self.current_position = 0.0
        self.duration = 0.0
    
    def load_video(self, video_path: str):
        """加载视频文件"""
        # TODO: 实现GUI中的MPV集成
        pass
    
    def load_subtitles(self, subtitle_path: str):
        """加载字幕文件"""
        # TODO: 实现字幕加载
        pass
    
    def play(self):
        """播放"""
        # TODO: 实现播放控制
        pass
    
    def pause(self):
        """暂停"""
        # TODO: 实现暂停控制
        pass
    
    def stop(self):
        """停止"""
        # TODO: 实现停止控制
        pass
    
    def seek(self, position: float):
        """跳转到指定位置"""
        # TODO: 实现位置跳转
        pass
    
    def get_position(self) -> float:
        """获取当前播放位置"""
        # TODO: 实现位置获取
        return self.current_position
    
    def get_duration(self) -> float:
        """获取视频总时长"""
        # TODO: 实现时长获取
        return self.duration
