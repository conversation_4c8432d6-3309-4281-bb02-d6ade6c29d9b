"""
FFmpeg服务模块
负责音视频处理相关功能
"""

import os
import subprocess
import shutil
import platform
from pathlib import Path
from typing import Optional, Dict, Any, List
import logging

from ..core.models import VideoInfo
from ..core.exceptions import FFmpegError, AudioExtractionError, FileNotFoundError
from ..config.settings import settings

logger = logging.getLogger(__name__)


class FFmpegService:
    """FFmpeg服务类"""

    def __init__(self, ffmpeg_path: str = None):
        self.ffmpeg_path = ffmpeg_path or settings.FFMPEG_PATH
        self.preferred_ffmpeg_path = None
        self._detect_best_ffmpeg()
        self._check_ffmpeg_availability()

    def _detect_best_ffmpeg(self):
        """检测最佳的FFmpeg路径，避免库冲突"""
        potential_paths = []

        if platform.system() == "Darwin":  # macOS
            # 优先使用homebrew版本，通常更稳定
            potential_paths = [
                "/opt/homebrew/bin/ffmpeg",
                "/usr/local/bin/ffmpeg",
                "/opt/local/bin/ffmpeg",  # MacPorts
                "ffmpeg"  # 系统PATH中的版本
            ]
        else:
            potential_paths = ["ffmpeg"]

        for path in potential_paths:
            if shutil.which(path):
                # 测试FFmpeg是否能正常工作
                if self._test_ffmpeg(path):
                    self.preferred_ffmpeg_path = path
                    logger.info(f"Using FFmpeg at: {path}")
                    break

        if self.preferred_ffmpeg_path:
            self.ffmpeg_path = self.preferred_ffmpeg_path

    def _test_ffmpeg(self, ffmpeg_path: str) -> bool:
        """测试FFmpeg是否能正常工作"""
        try:
            result = subprocess.run(
                [ffmpeg_path, "-version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False

    def _check_ffmpeg_availability(self):
        """检查FFmpeg是否可用"""
        if not shutil.which(self.ffmpeg_path):
            raise FFmpegError(f"FFmpeg not found at '{self.ffmpeg_path}'. Please ensure FFmpeg is installed and in PATH.")
    
    def extract_audio(self, video_path: str, output_path: Optional[str] = None) -> str:
        """
        从视频文件中提取音频

        Args:
            video_path: 视频文件路径
            output_path: 输出音频文件路径，如果为None则自动生成

        Returns:
            str: 提取的音频文件路径

        Raises:
            FileNotFoundError: 视频文件不存在
            AudioExtractionError: 音频提取失败
        """
        video_path = Path(video_path)
        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")

        if output_path is None:
            output_path = settings.get_temp_audio_path(video_path.stem)
        else:
            output_path = Path(output_path)

        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 尝试多种音频提取策略
        strategies = self._get_extraction_strategies(video_path, output_path)

        last_error = None
        for i, (strategy_name, command) in enumerate(strategies):
            logger.info(f"Trying extraction strategy {i+1}/{len(strategies)}: {strategy_name}")
            logger.info(f"Command: {' '.join(command)}")

            try:
                # 设置环境变量以避免库冲突
                env = self._get_clean_environment()

                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    timeout=300,  # 5分钟超时
                    env=env
                )

                if result.returncode == 0:
                    # 检查输出文件是否存在且有效
                    if output_path.exists() and output_path.stat().st_size > 0:
                        logger.info(f"Audio extracted successfully using {strategy_name}: {output_path}")
                        return str(output_path)
                    else:
                        logger.warning(f"Strategy {strategy_name} completed but output file is invalid")
                        continue
                else:
                    error_msg = f"Strategy {strategy_name} failed with return code {result.returncode}"
                    if result.stderr:
                        error_msg += f": {result.stderr}"
                    logger.warning(error_msg)
                    last_error = error_msg
                    continue

            except subprocess.TimeoutExpired:
                error_msg = f"Strategy {strategy_name} timed out"
                logger.warning(error_msg)
                last_error = error_msg
                continue
            except Exception as e:
                error_msg = f"Strategy {strategy_name} failed with exception: {e}"
                logger.warning(error_msg)
                last_error = error_msg
                continue

        # 所有策略都失败了
        final_error = f"All extraction strategies failed. Last error: {last_error}"
        logger.error(final_error)
        raise AudioExtractionError(final_error)

    def _get_extraction_strategies(self, video_path: Path, output_path: Path) -> List[tuple]:
        """获取音频提取策略列表"""
        strategies = []

        # 策略1: 标准PCM提取
        strategies.append((
            "Standard PCM",
            [
                self.ffmpeg_path,
                "-i", str(video_path),
                "-vn",
                "-acodec", "pcm_s16le",
                "-ar", str(settings.AUDIO_SAMPLE_RATE),
                "-ac", str(settings.AUDIO_CHANNELS),
                "-y",
                str(output_path)
            ]
        ))

        # 策略2: 使用libav而不是原生解码器
        strategies.append((
            "LibAV PCM",
            [
                self.ffmpeg_path,
                "-i", str(video_path),
                "-vn",
                "-c:a", "pcm_s16le",
                "-ar", str(settings.AUDIO_SAMPLE_RATE),
                "-ac", str(settings.AUDIO_CHANNELS),
                "-avoid_negative_ts", "make_zero",
                "-y",
                str(output_path)
            ]
        ))

        # 策略3: 简化参数
        strategies.append((
            "Simple extraction",
            [
                self.ffmpeg_path,
                "-i", str(video_path),
                "-vn",
                "-ar", "16000",
                "-ac", "1",
                "-y",
                str(output_path)
            ]
        ))

        # 策略4: 使用WAV格式但不指定编码器
        strategies.append((
            "Auto codec WAV",
            [
                self.ffmpeg_path,
                "-i", str(video_path),
                "-vn",
                "-f", "wav",
                "-ar", "16000",
                "-ac", "1",
                "-y",
                str(output_path)
            ]
        ))

        return strategies

    def _get_clean_environment(self) -> Dict[str, str]:
        """获取清理过的环境变量，避免库冲突"""
        env = os.environ.copy()

        # 在macOS上清理可能导致冲突的环境变量
        if platform.system() == "Darwin":
            # 移除可能导致库冲突的路径
            dyld_paths_to_remove = [
                "/opt/anaconda3/envs/video-subtitle-tool/lib",
                "/opt/anaconda3/lib"
            ]

            if "DYLD_LIBRARY_PATH" in env:
                paths = env["DYLD_LIBRARY_PATH"].split(":")
                cleaned_paths = [p for p in paths if not any(remove in p for remove in dyld_paths_to_remove)]
                if cleaned_paths:
                    env["DYLD_LIBRARY_PATH"] = ":".join(cleaned_paths)
                else:
                    del env["DYLD_LIBRARY_PATH"]

            # 确保使用系统的FFmpeg库
            env["DYLD_FALLBACK_LIBRARY_PATH"] = "/opt/homebrew/lib:/usr/local/lib:/usr/lib"

        return env
    
    def get_video_info(self, video_path: str) -> VideoInfo:
        """
        获取视频文件信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            VideoInfo: 视频信息对象
        """
        video_path = Path(video_path)
        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        command = [
            self.ffmpeg_path,
            "-i", str(video_path),
            "-f", "null",
            "-"
        ]
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True
            )
            
            # FFmpeg将信息输出到stderr
            info_text = result.stderr
            
            # 解析视频信息
            video_info = VideoInfo(path=str(video_path))
            
            # 这里可以添加更详细的信息解析
            # 目前先返回基本信息
            return video_info
            
        except Exception as e:
            logger.warning(f"Failed to get video info: {e}")
            return VideoInfo(path=str(video_path))
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_dir = settings.TEMP_DIR
            if temp_dir.exists():
                for file in temp_dir.glob("*_extracted_audio.*"):
                    file.unlink()
                logger.info("Temporary audio files cleaned up")
        except Exception as e:
            logger.warning(f"Failed to cleanup temp files: {e}")
