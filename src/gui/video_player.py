"""
内嵌视频播放器组件
使用python-mpv实现GUI内嵌播放器
"""

import logging
from pathlib import Path
from typing import Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
        QSlider, QLabel, QFrame, QMessageBox
    )
    from PyQt6.QtCore import Qt, QTimer, pyqtSignal
    from PyQt6.QtGui import QPalette
except ImportError:
    print("PyQt6 not installed")
    QWidget = object

try:
    # 在macOS上设置libmpv路径
    import os
    import platform
    import ctypes

    if platform.system() == "Darwin":
        # 设置macOS上的库路径
        os.environ['DYLD_LIBRARY_PATH'] = '/opt/homebrew/lib:' + os.environ.get('DYLD_LIBRARY_PATH', '')

        # 手动指定libmpv路径
        potential_paths = [
            '/opt/homebrew/lib/libmpv.dylib',
            '/usr/local/lib/libmpv.dylib',
            '/opt/local/lib/libmpv.dylib'
        ]

        libmpv_found = False
        for path in potential_paths:
            if os.path.exists(path):
                # 直接加载libmpv库
                try:
                    ctypes.CDLL(path)
                    os.environ['LIBMPV_PATH'] = path
                    print(f"Found libmpv at: {path}")
                    libmpv_found = True
                    break
                except OSError as e:
                    print(f"Failed to load {path}: {e}")
                    continue

        if not libmpv_found:
            print("Warning: libmpv not found in standard locations")

    import mpv
    MPV_AVAILABLE = True
    print("✅ python-mpv imported successfully")
except ImportError as e:
    print(f"❌ python-mpv not installed: {e}")
    MPV_AVAILABLE = False
    mpv = None
except OSError as e:
    print(f"❌ libmpv not found: {e}")
    print("Try: brew install mpv")
    MPV_AVAILABLE = False
    mpv = None

logger = logging.getLogger(__name__)


class VideoPlayerWidget(QWidget):
    """内嵌视频播放器组件"""
    
    # 信号定义
    position_changed = pyqtSignal(float)  # 播放位置变化
    duration_changed = pyqtSignal(float)  # 视频时长变化
    playback_finished = pyqtSignal()      # 播放完成
    error_occurred = pyqtSignal(str)      # 错误发生
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.mpv_player = None
        self.current_video = None
        self.current_subtitle = None
        self.is_playing = False
        self.duration = 0.0
        self.position = 0.0
        
        self.init_ui()
        self.init_mpv()
        
        # 定时器用于更新播放进度
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_position)
        self.update_timer.start(100)  # 每100ms更新一次
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 视频显示区域
        self.video_frame = QFrame()
        self.video_frame.setMinimumSize(640, 360)
        self.video_frame.setStyleSheet("""
            QFrame {
                background-color: black;
                border: 1px solid gray;
            }
        """)
        layout.addWidget(self.video_frame)
        
        # 控制面板
        control_layout = QHBoxLayout()
        
        # 播放/暂停按钮
        self.play_pause_btn = QPushButton("播放")
        self.play_pause_btn.clicked.connect(self.toggle_playback)
        self.play_pause_btn.setEnabled(False)
        control_layout.addWidget(self.play_pause_btn)
        
        # 停止按钮
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        # 进度条
        self.position_slider = QSlider(Qt.Orientation.Horizontal)
        self.position_slider.setMinimum(0)
        self.position_slider.setMaximum(1000)
        self.position_slider.sliderPressed.connect(self.on_slider_pressed)
        self.position_slider.sliderReleased.connect(self.on_slider_released)
        self.position_slider.setEnabled(False)
        control_layout.addWidget(self.position_slider)
        
        # 时间显示
        self.time_label = QLabel("00:00 / 00:00")
        control_layout.addWidget(self.time_label)
        
        layout.addLayout(control_layout)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: gray; font-size: 12px;")
        layout.addWidget(self.status_label)
    
    def init_mpv(self):
        """初始化MPV播放器"""
        if not MPV_AVAILABLE:
            self.status_label.setText("错误: python-mpv未安装")
            return

        try:
            # 创建MPV实例，使用更兼容的参数
            mpv_options = {
                'keep_open': True,  # 播放结束后保持打开
                'idle': True,  # 空闲模式
                'log_handler': self.mpv_log_handler,
                'loglevel': 'info'
            }

            # 尝试嵌入到QFrame中（可能在某些系统上不工作）
            try:
                wid = int(self.video_frame.winId())
                if wid > 0:
                    mpv_options['wid'] = str(wid)
                    mpv_options['vo'] = 'libmpv'
            except Exception as e:
                logger.warning(f"Cannot embed MPV in QFrame: {e}, using windowed mode")

            self.mpv_player = mpv.MPV(**mpv_options)
            
            # 设置事件回调
            @self.mpv_player.property_observer('time-pos')
            def time_observer(_name, value):
                if value is not None:
                    self.position = value
                    self.position_changed.emit(value)
            
            @self.mpv_player.property_observer('duration')
            def duration_observer(_name, value):
                if value is not None:
                    self.duration = value
                    self.duration_changed.emit(value)
            
            @self.mpv_player.event_callback('end-file')
            def end_file_handler(event):
                self.is_playing = False
                self.play_pause_btn.setText("播放")
                self.playback_finished.emit()
                self.status_label.setText("播放完成")
            
            self.status_label.setText("播放器已就绪")
            logger.info("MPV player initialized successfully")
            
        except Exception as e:
            error_msg = f"初始化MPV播放器失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.error_occurred.emit(error_msg)
            logger.error(error_msg)
    
    def mpv_log_handler(self, loglevel, component, message):
        """MPV日志处理器"""
        if loglevel in ['error', 'fatal']:
            logger.error(f"MPV {component}: {message}")
        elif loglevel == 'warn':
            logger.warning(f"MPV {component}: {message}")
        else:
            logger.debug(f"MPV {component}: {message}")
    
    def load_video(self, video_path: str, subtitle_path: Optional[str] = None):
        """加载视频文件"""
        if not self.mpv_player:
            self.error_occurred.emit("播放器未初始化")
            return False

        try:
            video_path = Path(video_path)
            if not video_path.exists():
                self.error_occurred.emit(f"视频文件不存在: {video_path}")
                return False

            self.current_video = str(video_path)
            self.current_subtitle = subtitle_path

            # 停止当前播放（如果有）
            try:
                self.mpv_player.stop()
            except:
                pass

            # 加载视频，使用更安全的方法
            try:
                self.mpv_player.loadfile(self.current_video)
                logger.info(f"Video file loaded: {video_path}")
            except Exception as e:
                # 如果loadfile失败，尝试设置filename属性
                try:
                    self.mpv_player.filename = self.current_video
                    logger.info(f"Video loaded via filename property: {video_path}")
                except Exception as e2:
                    raise Exception(f"Both loadfile and filename failed: {e}, {e2}")

            # 等待一下让视频加载
            import time
            time.sleep(0.1)

            # 加载字幕
            if subtitle_path:
                subtitle_path = Path(subtitle_path)
                if subtitle_path.exists():
                    try:
                        self.mpv_player.sub_add(str(subtitle_path))
                        logger.info(f"Subtitle loaded: {subtitle_path}")
                    except Exception as e:
                        logger.warning(f"Failed to load subtitle: {e}")
                        # 尝试使用sub-file属性
                        try:
                            self.mpv_player['sub-file'] = str(subtitle_path)
                            logger.info(f"Subtitle loaded via property: {subtitle_path}")
                        except Exception as e2:
                            logger.warning(f"Both subtitle loading methods failed: {e}, {e2}")
                else:
                    logger.warning(f"Subtitle file not found: {subtitle_path}")

            # 启用控制按钮
            self.play_pause_btn.setEnabled(True)
            self.stop_btn.setEnabled(True)
            self.position_slider.setEnabled(True)

            self.status_label.setText(f"已加载: {video_path.name}")
            logger.info(f"Video successfully loaded: {video_path}")
            return True

        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.error_occurred.emit(error_msg)
            logger.error(error_msg)
            return False
    
    def toggle_playback(self):
        """切换播放/暂停状态"""
        if not self.mpv_player or not self.current_video:
            return
        
        try:
            if self.is_playing:
                self.mpv_player.pause = True
                self.play_pause_btn.setText("播放")
                self.status_label.setText("已暂停")
                self.is_playing = False
            else:
                self.mpv_player.pause = False
                self.play_pause_btn.setText("暂停")
                self.status_label.setText("正在播放")
                self.is_playing = True
                
        except Exception as e:
            self.error_occurred.emit(f"播放控制失败: {str(e)}")
    
    def stop(self):
        """停止播放"""
        if not self.mpv_player:
            return
        
        try:
            self.mpv_player.stop()
            self.is_playing = False
            self.play_pause_btn.setText("播放")
            self.position_slider.setValue(0)
            self.time_label.setText("00:00 / 00:00")
            self.status_label.setText("已停止")
            
        except Exception as e:
            self.error_occurred.emit(f"停止播放失败: {str(e)}")
    
    def seek(self, position: float):
        """跳转到指定位置"""
        if not self.mpv_player or not self.current_video:
            return
        
        try:
            self.mpv_player.seek(position, reference='absolute')
        except Exception as e:
            logger.error(f"Seek failed: {e}")
    
    def on_slider_pressed(self):
        """进度条按下时暂停更新"""
        self.update_timer.stop()
    
    def on_slider_released(self):
        """进度条释放时跳转并恢复更新"""
        if self.duration > 0:
            position = (self.position_slider.value() / 1000.0) * self.duration
            self.seek(position)
        self.update_timer.start()
    
    def update_position(self):
        """更新播放位置显示"""
        if not self.mpv_player or not self.current_video:
            return
        
        try:
            # 更新进度条
            if self.duration > 0 and not self.position_slider.isSliderDown():
                progress = int((self.position / self.duration) * 1000)
                self.position_slider.setValue(progress)
            
            # 更新时间显示
            current_time = self.format_time(self.position)
            total_time = self.format_time(self.duration)
            self.time_label.setText(f"{current_time} / {total_time}")
            
        except Exception as e:
            logger.debug(f"Update position failed: {e}")
    
    def format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        if seconds < 0:
            seconds = 0
        
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.mpv_player:
            try:
                self.mpv_player.terminate()
            except:
                pass
        super().closeEvent(event)
