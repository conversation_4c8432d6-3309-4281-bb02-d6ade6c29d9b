"""
主窗口模块
视频字幕工具的主GUI界面
"""

import sys
import logging
from pathlib import Path
from typing import Optional

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QLabel, QPushButton, QFileDialog, QTextEdit, QProgressBar,
        QGroupBox, QComboBox, QCheckBox, QSpinBox, QTabWidget,
        QMessageBox, QSplitter, QFrame
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt6.QtGui import QFont, QIcon, QPixmap
except ImportError:
    print("PyQt6 not installed. Please run 'pip install PyQt6'")
    sys.exit(1)

from ..config.settings import settings
from ..services.ffmpeg_service import FFmpegService
from ..services.asr_service import ASRService
from ..services.subtitle_service import SubtitleService
from ..services.player_service import PlayerService
from ..utils.file_utils import is_video_file

logger = logging.getLogger(__name__)


class ProcessingThread(QThread):
    """视频处理线程"""
    
    progress_updated = pyqtSignal(str)  # 进度更新信号
    processing_finished = pyqtSignal(bool, str)  # 处理完成信号 (成功, 消息)
    
    def __init__(self, video_path: str, output_dir: str = None):
        super().__init__()
        self.video_path = video_path
        self.output_dir = output_dir
        self.ffmpeg_service = FFmpegService()
        self.asr_service = ASRService()
        self.subtitle_service = SubtitleService()
    
    def run(self):
        """运行视频处理"""
        try:
            video_path = Path(self.video_path)
            
            # 1. 提取音频
            self.progress_updated.emit("正在提取音频...")
            audio_path = self.ffmpeg_service.extract_audio(str(video_path))
            
            # 2. 语音识别
            self.progress_updated.emit("正在进行语音识别...")
            
            def progress_callback(message):
                self.progress_updated.emit(f"ASR: {message}")
            
            result = self.asr_service.transcribe(audio_path, progress_callback)
            
            if not result.success:
                self.processing_finished.emit(False, f"ASR失败: {result.error_message}")
                return
            
            # 3. 优化字幕片段
            self.progress_updated.emit("正在优化字幕片段...")
            optimized_segments = self.subtitle_service.optimize_segments(result.segments)
            
            # 4. 生成SRT文件
            self.progress_updated.emit("正在生成SRT文件...")
            if self.output_dir:
                output_path = Path(self.output_dir) / f"{video_path.stem}_generated.srt"
            else:
                output_path = settings.get_output_srt_path(video_path.stem)
            
            success = self.subtitle_service.generate_srt(optimized_segments, str(output_path))
            
            if success:
                self.processing_finished.emit(True, f"字幕生成成功: {output_path}")
            else:
                self.processing_finished.emit(False, "字幕生成失败")
                
        except Exception as e:
            self.processing_finished.emit(False, f"处理错误: {str(e)}")
        finally:
            # 清理临时文件
            self.ffmpeg_service.cleanup_temp_files()


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.current_video_path = None
        self.current_subtitle_path = None
        self.processing_thread = None
        self.player_service = PlayerService()
        
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("视频字幕AI工具")
        self.setGeometry(100, 100, settings.WINDOW_WIDTH, settings.WINDOW_HEIGHT)
        self.setMinimumSize(settings.WINDOW_MIN_WIDTH, settings.WINDOW_MIN_HEIGHT)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个选项卡
        self.create_main_tab()
        self.create_player_tab()
        self.create_settings_tab()
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_main_tab(self):
        """创建主要功能选项卡"""
        main_tab = QWidget()
        layout = QVBoxLayout(main_tab)
        
        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        
        # 视频文件选择
        video_layout = QHBoxLayout()
        self.video_label = QLabel("未选择视频文件")
        self.video_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
        self.select_video_btn = QPushButton("选择视频文件")
        video_layout.addWidget(self.video_label, 1)
        video_layout.addWidget(self.select_video_btn)
        file_layout.addLayout(video_layout)
        
        # 输出目录选择
        output_layout = QHBoxLayout()
        self.output_label = QLabel(f"输出目录: {settings.OUTPUT_DIR}")
        self.select_output_btn = QPushButton("选择输出目录")
        output_layout.addWidget(self.output_label, 1)
        output_layout.addWidget(self.select_output_btn)
        file_layout.addLayout(output_layout)
        
        layout.addWidget(file_group)
        
        # 处理控制区域
        control_group = QGroupBox("处理控制")
        control_layout = QVBoxLayout(control_group)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        self.process_btn = QPushButton("开始处理")
        self.process_btn.setEnabled(False)

        # 播放选项按钮
        play_layout = QHBoxLayout()
        self.play_btn = QPushButton("外部播放器")
        self.play_btn.setEnabled(False)
        self.load_player_btn = QPushButton("内嵌播放器")
        self.load_player_btn.setEnabled(False)

        play_layout.addWidget(self.play_btn)
        play_layout.addWidget(self.load_player_btn)

        button_layout.addWidget(self.process_btn)
        button_layout.addLayout(play_layout)
        button_layout.addStretch()
        
        control_layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        layout.addWidget(control_group)
        
        # 日志输出区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        self.tab_widget.addTab(main_tab, "主要功能")

    def create_player_tab(self):
        """创建播放器选项卡"""
        player_tab = QWidget()
        layout = QVBoxLayout(player_tab)

        # 导入播放器组件
        try:
            from .video_player import VideoPlayerWidget
            self.video_player = VideoPlayerWidget()

            # 连接播放器信号
            self.video_player.error_occurred.connect(self.on_player_error)
            self.video_player.playback_finished.connect(self.on_playback_finished)

            layout.addWidget(self.video_player)

            # 播放器控制按钮
            control_layout = QHBoxLayout()

            self.load_video_btn = QPushButton("加载当前视频")
            self.load_video_btn.clicked.connect(self.load_video_to_player)
            self.load_video_btn.setEnabled(False)
            control_layout.addWidget(self.load_video_btn)

            self.external_play_btn = QPushButton("外部播放器")
            self.external_play_btn.clicked.connect(self.play_video_external)
            self.external_play_btn.setEnabled(False)
            control_layout.addWidget(self.external_play_btn)

            control_layout.addStretch()
            layout.addLayout(control_layout)

        except ImportError as e:
            # 如果播放器组件导入失败，显示错误信息
            error_label = QLabel(f"播放器组件加载失败: {e}\n请确保已安装python-mpv")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("color: red; font-size: 14px;")
            layout.addWidget(error_label)

            self.video_player = None

        self.tab_widget.addTab(player_tab, "视频播放器")

    def create_settings_tab(self):
        """创建设置选项卡"""
        settings_tab = QWidget()
        layout = QVBoxLayout(settings_tab)
        
        # Whisper模型设置
        model_group = QGroupBox("Whisper模型设置")
        model_layout = QVBoxLayout(model_group)
        
        # 模型选择
        model_select_layout = QHBoxLayout()
        model_select_layout.addWidget(QLabel("模型:"))
        self.model_combo = QComboBox()
        self.model_combo.addItems(["tiny", "base", "small", "medium", "large"])
        self.model_combo.setCurrentText(settings.DEFAULT_MODEL_NAME)
        model_select_layout.addWidget(self.model_combo)
        model_select_layout.addStretch()
        model_layout.addLayout(model_select_layout)
        
        # 语言设置
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("语言:"))
        self.language_combo = QComboBox()
        self.language_combo.addItems(["自动检测", "中文(zh)", "英文(en)", "日文(ja)", "韩文(ko)"])
        lang_layout.addWidget(self.language_combo)
        lang_layout.addStretch()
        model_layout.addLayout(lang_layout)
        
        # 设备选择
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("设备:"))
        self.device_combo = QComboBox()
        self.device_combo.addItems(["cpu", "cuda", "mps"])
        self.device_combo.setCurrentText(settings.DEFAULT_DEVICE)
        device_layout.addWidget(self.device_combo)
        device_layout.addStretch()
        model_layout.addLayout(device_layout)
        
        layout.addWidget(model_group)
        
        # 字幕设置
        subtitle_group = QGroupBox("字幕设置")
        subtitle_layout = QVBoxLayout(subtitle_group)
        
        # 最大字幕长度
        length_layout = QHBoxLayout()
        length_layout.addWidget(QLabel("最大字符数:"))
        self.max_length_spin = QSpinBox()
        self.max_length_spin.setRange(20, 200)
        self.max_length_spin.setValue(settings.MAX_SUBTITLE_LENGTH)
        length_layout.addWidget(self.max_length_spin)
        length_layout.addStretch()
        subtitle_layout.addLayout(length_layout)
        
        layout.addWidget(subtitle_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(settings_tab, "设置")

    def setup_connections(self):
        """设置信号连接"""
        self.select_video_btn.clicked.connect(self.select_video_file)
        self.select_output_btn.clicked.connect(self.select_output_directory)
        self.process_btn.clicked.connect(self.start_processing)
        self.play_btn.clicked.connect(self.play_video_external)
        self.load_player_btn.clicked.connect(self.load_video_to_player)

        # 设置变更连接
        self.model_combo.currentTextChanged.connect(self.update_model_settings)
        self.language_combo.currentTextChanged.connect(self.update_language_settings)
        self.device_combo.currentTextChanged.connect(self.update_device_settings)

    def select_video_file(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v);;所有文件 (*)"
        )

        if file_path:
            if is_video_file(file_path):
                self.current_video_path = file_path
                self.video_label.setText(f"视频文件: {Path(file_path).name}")
                self.process_btn.setEnabled(True)

                # 启用播放器相关按钮
                self.play_btn.setEnabled(True)
                self.load_player_btn.setEnabled(True)
                if hasattr(self, 'load_video_btn'):
                    self.load_video_btn.setEnabled(True)
                if hasattr(self, 'external_play_btn'):
                    self.external_play_btn.setEnabled(True)

                self.log_message(f"已选择视频文件: {file_path}")
            else:
                QMessageBox.warning(self, "警告", "不支持的视频格式")

    def select_output_directory(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            str(settings.OUTPUT_DIR)
        )

        if dir_path:
            self.output_label.setText(f"输出目录: {dir_path}")
            self.log_message(f"输出目录设置为: {dir_path}")

    def start_processing(self):
        """开始处理视频"""
        if not self.current_video_path:
            QMessageBox.warning(self, "警告", "请先选择视频文件")
            return

        # 禁用按钮
        self.process_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 清空日志
        self.log_text.clear()
        self.log_message("开始处理视频...")

        # 获取输出目录
        output_dir = None
        if "输出目录:" in self.output_label.text():
            output_dir = self.output_label.text().replace("输出目录: ", "")

        # 创建并启动处理线程
        self.processing_thread = ProcessingThread(self.current_video_path, output_dir)
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.processing_finished.connect(self.processing_completed)
        self.processing_thread.start()

    def update_progress(self, message: str):
        """更新进度信息"""
        self.log_message(message)
        self.statusBar().showMessage(message)

    def processing_completed(self, success: bool, message: str):
        """处理完成"""
        self.progress_bar.setVisible(False)
        self.process_btn.setEnabled(True)

        if success:
            self.log_message(f"✓ {message}")
            self.statusBar().showMessage("处理完成")
            self.play_btn.setEnabled(True)

            # 设置字幕文件路径
            if "字幕生成成功:" in message:
                self.current_subtitle_path = message.split(": ")[1]

            QMessageBox.information(self, "成功", message)
        else:
            self.log_message(f"✗ {message}")
            self.statusBar().showMessage("处理失败")
            QMessageBox.critical(self, "错误", message)

    def play_video_external(self):
        """播放视频"""
        if not self.current_video_path:
            QMessageBox.warning(self, "警告", "没有可播放的视频")
            return

        try:
            self.log_message("正在启动视频播放...")
            self.statusBar().showMessage("启动视频播放器...")

            # 使用异步方式启动播放器
            self.player_service.play_video_with_subtitles(
                self.current_video_path,
                self.current_subtitle_path
            )

            self.log_message("✓ 视频播放器已启动")
            self.statusBar().showMessage("视频播放器已启动")

            # 显示提示信息和备用选项
            reply = QMessageBox.question(
                self,
                "播放器启动",
                "视频播放器已在后台启动。\n\n如果没有看到播放窗口，是否要尝试其他播放方式？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.show_alternative_playback_options()

        except Exception as e:
            error_msg = f"无法播放视频: {str(e)}"
            QMessageBox.critical(self, "播放错误", error_msg)
            self.log_message(f"✗ 播放失败: {str(e)}")
            self.statusBar().showMessage("播放失败")

    def load_video_to_player(self):
        """加载视频到内嵌播放器"""
        if not self.current_video_path:
            QMessageBox.warning(self, "警告", "请先选择视频文件")
            return

        if not hasattr(self, 'video_player') or self.video_player is None:
            QMessageBox.warning(self, "警告", "内嵌播放器不可用")
            return

        try:
            self.log_message("正在加载视频到内嵌播放器...")

            # 加载视频到播放器
            success = self.video_player.load_video(
                self.current_video_path,
                self.current_subtitle_path
            )

            if success:
                self.log_message("✓ 视频已加载到播放器")
                self.statusBar().showMessage("视频已加载")

                # 切换到播放器选项卡
                self.tab_widget.setCurrentIndex(1)  # 播放器选项卡是第二个

                QMessageBox.information(
                    self,
                    "加载成功",
                    "视频已加载到内嵌播放器。\n请切换到'视频播放器'选项卡查看。"
                )
            else:
                self.log_message("✗ 视频加载失败")

        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            QMessageBox.critical(self, "加载错误", error_msg)
            self.log_message(f"✗ {error_msg}")

    def on_player_error(self, error_message: str):
        """处理播放器错误"""
        self.log_message(f"✗ 播放器错误: {error_message}")
        QMessageBox.critical(self, "播放器错误", error_message)

    def on_playback_finished(self):
        """播放完成处理"""
        self.log_message("✓ 视频播放完成")
        self.statusBar().showMessage("播放完成")

    def update_model_settings(self, model_name: str):
        """更新模型设置"""
        settings.update_model_name(model_name)
        self.log_message(f"模型设置更新为: {model_name}")

    def update_language_settings(self, language_text: str):
        """更新语言设置"""
        language_map = {
            "自动检测": None,
            "中文(zh)": "zh",
            "英文(en)": "en",
            "日文(ja)": "ja",
            "韩文(ko)": "ko"
        }
        language = language_map.get(language_text, None)
        settings.update_language(language)
        self.log_message(f"语言设置更新为: {language_text}")

    def update_device_settings(self, device: str):
        """更新设备设置"""
        settings.update_device(device)
        self.log_message(f"设备设置更新为: {device}")

    def log_message(self, message: str):
        """添加日志消息"""
        self.log_text.append(f"[{self.get_current_time()}] {message}")
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def get_current_time(self):
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def show_alternative_playback_options(self):
        """显示备用播放选项"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QPushButton, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle("备用播放选项")
        dialog.setFixedSize(400, 300)

        layout = QVBoxLayout(dialog)

        # 说明文本
        info_label = QLabel(
            "如果MPV播放器没有显示窗口，可以尝试以下选项："
        )
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 选项按钮
        btn_open_folder = QPushButton("打开输出文件夹")
        btn_open_folder.clicked.connect(self.open_output_folder)
        layout.addWidget(btn_open_folder)

        btn_copy_path = QPushButton("复制视频文件路径")
        btn_copy_path.clicked.connect(self.copy_video_path)
        layout.addWidget(btn_copy_path)

        btn_copy_subtitle = QPushButton("复制字幕文件路径")
        btn_copy_subtitle.clicked.connect(self.copy_subtitle_path)
        layout.addWidget(btn_copy_subtitle)

        btn_manual_command = QPushButton("显示手动播放命令")
        btn_manual_command.clicked.connect(self.show_manual_command)
        layout.addWidget(btn_manual_command)

        btn_close = QPushButton("关闭")
        btn_close.clicked.connect(dialog.close)
        layout.addWidget(btn_close)

        dialog.exec()

    def open_output_folder(self):
        """打开输出文件夹"""
        import subprocess
        import platform

        output_dir = settings.OUTPUT_DIR

        try:
            if platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(output_dir)])
            elif platform.system() == "Windows":
                subprocess.run(["explorer", str(output_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(output_dir)])

            self.log_message(f"已打开输出文件夹: {output_dir}")
        except Exception as e:
            self.log_message(f"无法打开文件夹: {e}")

    def copy_video_path(self):
        """复制视频文件路径到剪贴板"""
        if self.current_video_path:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(self.current_video_path)
            self.log_message(f"已复制视频路径: {self.current_video_path}")
        else:
            self.log_message("没有选择视频文件")

    def copy_subtitle_path(self):
        """复制字幕文件路径到剪贴板"""
        if self.current_subtitle_path:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(self.current_subtitle_path)
            self.log_message(f"已复制字幕路径: {self.current_subtitle_path}")
        else:
            self.log_message("没有生成字幕文件")

    def show_manual_command(self):
        """显示手动播放命令"""
        if not self.current_video_path:
            QMessageBox.warning(self, "警告", "没有选择视频文件")
            return

        # 构建手动命令
        command_parts = ["mpv", f'"{self.current_video_path}"']
        if self.current_subtitle_path:
            command_parts.append(f'--sub-file="{self.current_subtitle_path}"')

        command = " ".join(command_parts)

        # 显示命令
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton

        dialog = QDialog(self)
        dialog.setWindowTitle("手动播放命令")
        dialog.setFixedSize(600, 200)

        layout = QVBoxLayout(dialog)

        info_label = QLabel("请在终端中运行以下命令：")
        layout.addWidget(info_label)

        command_text = QTextEdit()
        command_text.setPlainText(command)
        command_text.setReadOnly(True)
        layout.addWidget(command_text)

        btn_copy = QPushButton("复制命令")
        btn_copy.clicked.connect(lambda: self.copy_command_to_clipboard(command))
        layout.addWidget(btn_copy)

        btn_close = QPushButton("关闭")
        btn_close.clicked.connect(dialog.close)
        layout.addWidget(btn_close)

        dialog.exec()

    def copy_command_to_clipboard(self, command: str):
        """复制命令到剪贴板"""
        from PyQt6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(command)
        self.log_message("已复制命令到剪贴板")


def setup_qt_environment():
    """设置Qt环境变量"""
    import os
    import platform
    import sys
    from pathlib import Path

    try:
        import PyQt6
        pyqt6_dir = Path(PyQt6.__file__).parent

        # 获取当前Python环境的site-packages路径
        site_packages_paths = []
        for path in sys.path:
            if 'site-packages' in path:
                site_packages_paths.append(Path(path))

        # 尝试多个可能的插件路径
        potential_plugin_paths = [
            pyqt6_dir / "Qt6" / "plugins",
            pyqt6_dir / "Qt" / "plugins",
            pyqt6_dir / "plugins",
        ]

        # 添加从site-packages路径推导的路径
        for site_path in site_packages_paths:
            potential_plugin_paths.extend([
                site_path / "PyQt6" / "Qt6" / "plugins",
                site_path / "PyQt6" / "Qt" / "plugins",
                site_path / "PyQt6" / "plugins",
            ])

        # 添加一些常见的系统路径
        potential_plugin_paths.extend([
            Path("/opt/anaconda3/lib/python3.12/site-packages/PyQt6/Qt6/plugins"),
            Path("/opt/anaconda3/envs/video-subtitle-tool/lib/python3.12/site-packages/PyQt6/Qt6/plugins"),
            Path("/opt/homebrew/lib/python3.12/site-packages/PyQt6/Qt6/plugins"),
        ])

        plugin_path_found = False
        working_plugin_path = None

        for plugins_dir in potential_plugin_paths:
            if plugins_dir.exists():
                # 检查平台特定的插件
                platform_plugins = plugins_dir / "platforms"
                if platform_plugins.exists():
                    if platform.system() == "Darwin":
                        cocoa_plugin = platform_plugins / "libqcocoa.dylib"
                        if cocoa_plugin.exists():
                            working_plugin_path = plugins_dir
                            plugin_path_found = True
                            break
                    elif platform.system() == "Linux":
                        xcb_plugin = platform_plugins / "libqxcb.so"
                        if xcb_plugin.exists():
                            working_plugin_path = plugins_dir
                            plugin_path_found = True
                            break
                    elif platform.system() == "Windows":
                        windows_plugin = platform_plugins / "qwindows.dll"
                        if windows_plugin.exists():
                            working_plugin_path = plugins_dir
                            plugin_path_found = True
                            break

        if plugin_path_found and working_plugin_path:
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(working_plugin_path)
            print(f"✅ Set QT_QPA_PLATFORM_PLUGIN_PATH to: {working_plugin_path}")
        else:
            print("⚠️  Warning: Could not find Qt platform plugins")
            # 尝试让Qt自动查找
            if 'QT_QPA_PLATFORM_PLUGIN_PATH' in os.environ:
                del os.environ['QT_QPA_PLATFORM_PLUGIN_PATH']

        # 设置其他Qt环境变量
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'

        # 对于macOS，确保使用正确的平台
        if platform.system() == "Darwin":
            os.environ['QT_QPA_PLATFORM'] = 'cocoa'
        elif platform.system() == "Linux":
            os.environ['QT_QPA_PLATFORM'] = 'xcb'

        return True

    except ImportError:
        print("❌ PyQt6 not found")
        return False


def launch_gui():
    """启动GUI应用"""
    # 设置Qt环境
    if not setup_qt_environment():
        print("❌ Qt环境设置失败")
        return

    app = QApplication(sys.argv)
    app.setApplicationName("视频字幕AI工具")
    app.setApplicationVersion("1.0.0")

    window = MainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    launch_gui()
