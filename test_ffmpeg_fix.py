#!/usr/bin/env python3
"""
测试FFmpeg修复
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.services.ffmpeg_service import FFmpegService
from src.config.settings import settings

def test_ffmpeg_service():
    """测试FFmpeg服务"""
    print("=== FFmpeg服务测试 ===")
    
    try:
        # 创建FFmpeg服务实例
        ffmpeg_service = FFmpegService()
        print(f"✅ FFmpeg服务初始化成功")
        print(f"使用的FFmpeg路径: {ffmpeg_service.ffmpeg_path}")
        
        # 检查测试视频文件
        test_video = Path("resources/test.mp4")
        if not test_video.exists():
            print(f"❌ 测试视频文件不存在: {test_video}")
            return False
        
        print(f"📹 测试视频文件: {test_video}")
        print(f"文件大小: {test_video.stat().st_size / 1024 / 1024:.2f} MB")
        
        # 尝试提取音频
        print("\n开始音频提取测试...")
        audio_path = ffmpeg_service.extract_audio(str(test_video))
        
        # 检查输出文件
        audio_file = Path(audio_path)
        if audio_file.exists():
            print(f"✅ 音频提取成功: {audio_path}")
            print(f"音频文件大小: {audio_file.stat().st_size / 1024:.2f} KB")
            
            # 清理测试文件
            audio_file.unlink()
            print("🧹 测试文件已清理")
            return True
        else:
            print(f"❌ 音频文件未生成: {audio_path}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始FFmpeg修复测试...")
    
    success = test_ffmpeg_service()
    
    if success:
        print("\n🎉 所有测试通过！FFmpeg服务工作正常。")
        return 0
    else:
        print("\n💥 测试失败！请检查错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
