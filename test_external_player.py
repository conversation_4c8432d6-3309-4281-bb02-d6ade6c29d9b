#!/usr/bin/env python3
"""
测试外部播放器
"""

import sys
import subprocess
import time
from pathlib import Path

def test_direct_mpv():
    """直接测试MPV命令"""
    print("🎬 直接测试MPV命令...")
    
    test_video = Path("resources/test.mp4")
    test_subtitle = Path("output/test_generated.srt")
    
    if not test_video.exists():
        print(f"❌ 测试视频不存在: {test_video}")
        return False
    
    # 构建命令
    command = [
        "mpv",
        str(test_video),
        "--force-window=yes",
        "--geometry=50%:50%",
        "--autofit=80%",
        "--ontop",
        "--focus-on-open",
        "--ao=coreaudio",
        "--cocoa-force-dedicated-gpu=yes",
    ]
    
    if test_subtitle.exists():
        command.extend([
            f"--sub-file={test_subtitle}",
            "--sub-font-size=36"
        ])
        print(f"📝 将加载字幕: {test_subtitle}")
    
    print(f"🚀 执行命令: {' '.join(command)}")
    
    try:
        # 启动MPV
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ MPV进程已启动，PID: {process.pid}")
        print("💡 请检查是否有MPV窗口出现")
        print("⏳ 等待5秒检查进程状态...")
        
        # 等待5秒检查状态
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ MPV进程仍在运行，应该有窗口显示")
            print("🔍 如果没有看到窗口，可能是macOS权限问题")
            
            # 等待用户确认
            input("👀 请检查MPV窗口是否显示，然后按Enter继续...")
            
            # 终止进程
            process.terminate()
            process.wait()
            print("🛑 MPV进程已终止")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ MPV进程已退出，返回码: {process.returncode}")
            if stderr:
                print(f"错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动MPV失败: {e}")
        return False

def test_player_service():
    """测试播放器服务"""
    print("\n🎮 测试播放器服务...")
    
    # 添加src目录到Python路径
    sys.path.insert(0, str(Path(__file__).parent / "src"))
    
    try:
        from src.services.player_service import PlayerService
        
        test_video = Path("resources/test.mp4")
        test_subtitle = Path("output/test_generated.srt")
        
        if not test_video.exists():
            print(f"❌ 测试视频不存在: {test_video}")
            return False
        
        # 创建播放器服务
        player_service = PlayerService()
        print("✅ PlayerService初始化成功")
        
        # 播放视频
        subtitle_path = str(test_subtitle) if test_subtitle.exists() else None
        player_service.play_video_with_subtitles(str(test_video), subtitle_path)
        
        print("✅ 播放器服务调用成功")
        print("💡 请检查是否有MPV窗口出现")
        
        return True
        
    except Exception as e:
        print(f"❌ 播放器服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_mpv_installation():
    """检查MPV安装"""
    print("🔍 检查MPV安装...")
    
    try:
        result = subprocess.run(
            ["mpv", "--version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ MPV已安装: {version_line}")
            return True
        else:
            print(f"❌ MPV版本检查失败: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ MPV未安装或不在PATH中")
        print("💡 请运行: brew install mpv")
        return False
    except Exception as e:
        print(f"❌ MPV检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎬 外部播放器测试")
    print("=" * 50)
    
    # 检查MPV安装
    if not check_mpv_installation():
        return 1
    
    print()
    
    # 测试直接MPV命令
    if test_direct_mpv():
        print("\n✅ 直接MPV测试成功")
    else:
        print("\n❌ 直接MPV测试失败")
        return 1
    
    # 测试播放器服务
    if test_player_service():
        print("\n✅ 播放器服务测试成功")
    else:
        print("\n❌ 播放器服务测试失败")
        return 1
    
    print("\n🎉 所有测试完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
